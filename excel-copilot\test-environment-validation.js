#!/usr/bin/env node

/**
 * 🧪 TESTE DE VALIDAÇÃO DE AMBIENTE - EXCEL COPILOT
 * 
 * Este script testa se o sistema de validação de ambiente está funcionando
 * corretamente após as correções de segurança implementadas.
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 INICIANDO TESTES DE VALIDAÇÃO DE AMBIENTE\n');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

let testsPassed = 0;
let testsFailed = 0;

function logTest(name, passed, message = '') {
  const status = passed ? `${colors.green}✅ PASSOU${colors.reset}` : `${colors.red}❌ FALHOU${colors.reset}`;
  console.log(`${status} ${name}`);
  if (message) {
    console.log(`   ${colors.yellow}→${colors.reset} ${message}`);
  }
  
  if (passed) {
    testsPassed++;
  } else {
    testsFailed++;
  }
}

// TESTE 1: Verificar se arquivos com credenciais foram removidos
console.log(`${colors.blue}📋 TESTE 1: Verificação de Remoção de Credenciais${colors.reset}`);

const sensitiveFiles = [
  '.env.local',
  '.next/standalone/.env.production'
];

sensitiveFiles.forEach(file => {
  const exists = fs.existsSync(file);
  logTest(
    `Arquivo ${file} removido`,
    !exists,
    exists ? 'CRÍTICO: Arquivo com credenciais ainda existe!' : 'Arquivo removido com sucesso'
  );
});

// TESTE 2: Verificar se .gitignore está configurado corretamente
console.log(`\n${colors.blue}📋 TESTE 2: Verificação do .gitignore${colors.reset}`);

if (fs.existsSync('.gitignore')) {
  const gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
  
  const requiredPatterns = [
    '.env',
    '.env*.local',
    '.env.production',
    'credentials.json'
  ];
  
  requiredPatterns.forEach(pattern => {
    const isIgnored = gitignoreContent.includes(pattern);
    logTest(
      `Padrão "${pattern}" no .gitignore`,
      isIgnored,
      isIgnored ? 'Padrão encontrado' : 'CRÍTICO: Padrão ausente!'
    );
  });
} else {
  logTest('Arquivo .gitignore existe', false, 'CRÍTICO: .gitignore não encontrado!');
}

// TESTE 3: Verificar se next.config.js não tem ignoreBuildErrors
console.log(`\n${colors.blue}📋 TESTE 3: Verificação de Configuração TypeScript${colors.reset}`);

if (fs.existsSync('next.config.js')) {
  const configContent = fs.readFileSync('next.config.js', 'utf8');
  
  const hasIgnoreBuildErrors = configContent.includes('ignoreBuildErrors: true');
  logTest(
    'ignoreBuildErrors removido do next.config.js',
    !hasIgnoreBuildErrors,
    hasIgnoreBuildErrors ? 'CRÍTICO: ignoreBuildErrors ainda está ativo!' : 'Validação TypeScript habilitada'
  );
} else {
  logTest('next.config.js existe', false, 'CRÍTICO: next.config.js não encontrado!');
}

// TESTE 4: Verificar se scripts não têm DISABLE_ENV_VALIDATION
console.log(`\n${colors.blue}📋 TESTE 4: Verificação de Scripts de Build${colors.reset}`);

if (fs.existsSync('scripts/vercel-build-safe.js')) {
  const scriptContent = fs.readFileSync('scripts/vercel-build-safe.js', 'utf8');
  
  const hasDisableValidation = scriptContent.includes('DISABLE_ENV_VALIDATION: \'true\'');
  logTest(
    'DISABLE_ENV_VALIDATION removido dos scripts',
    !hasDisableValidation,
    hasDisableValidation ? 'CRÍTICO: Bypass de validação ainda ativo!' : 'Validação de ambiente obrigatória'
  );
} else {
  logTest('Script vercel-build-safe.js existe', false, 'Script não encontrado');
}

// TESTE 5: Verificar se unified-environment.ts foi corrigido
console.log(`\n${colors.blue}📋 TESTE 5: Verificação do Sistema de Validação Unificado${colors.reset}`);

if (fs.existsSync('src/config/unified-environment.ts')) {
  const envContent = fs.readFileSync('src/config/unified-environment.ts', 'utf8');
  
  // Verificar se DEV_DISABLE_VALIDATION está sempre false
  const hasSecureValidation = envContent.includes('DEV_DISABLE_VALIDATION: false') && 
                              envContent.includes('// SEMPRE false para garantir validação');
  
  logTest(
    'Sistema de validação seguro implementado',
    hasSecureValidation,
    hasSecureValidation ? 'Validação sempre ativa' : 'CRÍTICO: Bypass ainda possível!'
  );
  
  // Verificar se há validação de produção
  const hasProductionValidation = envContent.includes('CRÍTICO: DISABLE_ENV_VALIDATION está ativo em produção');
  
  logTest(
    'Validação específica para produção implementada',
    hasProductionValidation,
    hasProductionValidation ? 'Proteção contra bypass em produção' : 'Validação de produção ausente'
  );
} else {
  logTest('unified-environment.ts existe', false, 'CRÍTICO: Sistema de validação não encontrado!');
}

// TESTE 6: Verificar se documentação de segurança foi criada
console.log(`\n${colors.blue}📋 TESTE 6: Verificação de Documentação de Segurança${colors.reset}`);

const securityDocs = [
  'CONFIGURACAO_SEGURA_CREDENCIAIS.md'
];

securityDocs.forEach(doc => {
  const exists = fs.existsSync(doc);
  logTest(
    `Documentação ${doc} criada`,
    exists,
    exists ? 'Documentação disponível' : 'Documentação ausente'
  );
});

// TESTE 7: Verificar se não há arquivos de configuração duplicados
console.log(`\n${colors.blue}📋 TESTE 7: Verificação de Arquivos Duplicados${colors.reset}`);

const duplicateConfigs = [
  'next.config.clean.js',
  'next.config.js.backup'
];

duplicateConfigs.forEach(file => {
  const exists = fs.existsSync(file);
  logTest(
    `Arquivo duplicado ${file} removido`,
    !exists,
    exists ? 'Arquivo duplicado ainda existe' : 'Configuração consolidada'
  );
});

// RESUMO DOS TESTES
console.log(`\n${colors.bold}📊 RESUMO DOS TESTES${colors.reset}`);
console.log(`${colors.green}✅ Testes Aprovados: ${testsPassed}${colors.reset}`);
console.log(`${colors.red}❌ Testes Falharam: ${testsFailed}${colors.reset}`);

const totalTests = testsPassed + testsFailed;
const successRate = ((testsPassed / totalTests) * 100).toFixed(1);

console.log(`\n${colors.blue}📈 Taxa de Sucesso: ${successRate}%${colors.reset}`);

if (testsFailed === 0) {
  console.log(`\n${colors.green}🎉 TODOS OS TESTES PASSARAM!${colors.reset}`);
  console.log(`${colors.green}✅ Sistema de configuração seguro implementado com sucesso.${colors.reset}`);
  process.exit(0);
} else {
  console.log(`\n${colors.red}⚠️ ALGUNS TESTES FALHARAM!${colors.reset}`);
  console.log(`${colors.red}❌ Revise as correções de segurança antes de prosseguir.${colors.reset}`);
  process.exit(1);
}
